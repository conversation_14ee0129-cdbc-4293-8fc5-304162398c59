{"name": "platon-header", "version": "0.0.1", "private": true, "engines": {"node": ">=22.17.1"}, "main": "lib/index.js", "scripts": {"build": "gulp bundle", "clean": "gulp clean", "test": "gulp test"}, "dependencies": {"@microsoft/decorators": "1.21.1", "@microsoft/sp-application-base": "1.21.1", "@microsoft/sp-core-library": "1.21.1", "@microsoft/sp-dialog": "1.21.1", "botframework-webchat": "^4.15.0", "react-icons": "^5.5.0", "tslib": "2.3.1"}, "devDependencies": {"@microsoft/eslint-config-spfx": "1.21.1", "@microsoft/eslint-plugin-spfx": "1.21.1", "@microsoft/rush-stack-compiler-4.7": "0.1.0", "@microsoft/sp-build-web": "1.21.1", "@microsoft/sp-module-interfaces": "1.21.1", "@rushstack/eslint-config": "4.0.1", "@types/webpack-env": "~1.15.2", "ajv": "^6.12.5", "eslint": "8.57.0", "gulp": "4.0.2", "typescript": "4.7.4"}}