{"extends": "./node_modules/@microsoft/rush-stack-compiler-4.7/includes/tsconfig-web.json", "compilerOptions": {"target": "es6", "forceConsistentCasingInFileNames": true, "module": "esnext", "moduleResolution": "node", "jsx": "react", "declaration": true, "sourceMap": true, "experimentalDecorators": true, "skipLibCheck": true, "outDir": "lib", "inlineSources": false, "noImplicitAny": true, "typeRoots": ["./node_modules/@types", "./node_modules/@microsoft"], "types": ["webpack-env"], "lib": ["es6", "dom", "es2015.collection", "es2015.promise", "es2015", "es2017", "es2019"]}, "include": ["src/**/*.ts", "src/**/*.tsx", "src/extensions/platonSiteCustomization/loc/en-us.js", "src/extensions/platonSiteCustomization/loc/ja-jp.js"]}