/** ===================== PLATON CHATBOT (Localized EN/jp) ===================== */

type PlatonChatHandle = {
  open: () => void;
  close: () => void;
  dispose: () => void;
  /** Switch language at runtime: e.g., handle.setLanguage('jp') */
  setLanguage: (lang: string) => void;
};

type PlatonChatOptions = {
  webAbsoluteUrl?: string;
  listTitle?: string; // default "ChatbotQ&A"
  brandName?: string; // default localized fallback
  /** force language: 'en' | 'jp' | ... (otherwise autodetects) */
  lang?: string;
};

type QaRow = {
  Id: number;
  Title?: string;
  Answer?: string;
  Category?: string;
  Keywords?: string;
  Keyword?: string;
  Link?: { Url?: string; Description?: string } | string | undefined;
  link?: { Url?: string; Description?: string } | string | undefined;
};

type QaResponseNoMeta = { value: QaRow[] };

type QAItem = {
  Id: number;
  Title: string;
  Answer: string;
  Category?: string;
  Keywords?: string;
  Link?: { Url?: string; Description?: string } | string;
};

/** ---- Extra helper types to avoid `any` ---- */
interface SPPageContextInfo {
  webAbsoluteUrl?: string;
  currentUICultureName?: string;
  currentCultureName?: string;
}
type WindowWithSP = Window & { _spPageContextInfo?: SPPageContextInfo };
type KeyboardEventWithIME = KeyboardEvent & { isComposing?: boolean };

/* -------------------- i18n -------------------- */
type MsgKey =
  | "brandDefault"
  | "ariaOpen"
  | "ariaClose"
  | "typing"
  | "openLink"
  | "placeholder"
  | "hiLine1"
  | "hiLine2"
  | "errorGeneric"
  | "errorLoad"
  | "noMatch"
  | "quick.startingPoint"
  | "quick.blockTraining"
  | "quick.keyUserInfo"
  | "quick.peakCapture"
  | "quick.contactUs"
  | "altLogo"
  | "redirecting.startingPoint"
  | "redirecting.blockTraining"
  | "redirecting.keyUserInfo"
  | "redirecting.peakCapture"
  | "redirecting.contactUs";

const messages: Record<string, Record<MsgKey, string>> = {
  en: {
    brandDefault: "AskPLATON",
    ariaOpen: "Open chat",
    ariaClose: "Close chat",
    typing: "Typing…",
    openLink: "Open link",
    placeholder: "Please type your request",
    hiLine1:
      "Hi, I'm your AskPLATON bot. I can help with training videos, documents, and more.",
    hiLine2: "You can type your request below, or use these quick actions:",
    errorGeneric: "Something went wrong. Please try again.",
    errorLoad:
      "Sorry, I couldn't load answers right now. Please try again later.",
    noMatch:
      "No exact match found. Try different keywords or check the PLATON pages.",
    "quick.startingPoint": "Starting Point",
    "quick.blockTraining": "Block Your Training",
    "quick.keyUserInfo": "Key User Info",
    "quick.peakCapture": "Peak Capture",
    "quick.contactUs": "Contact Us",
    altLogo: "Platon",
    "redirecting.startingPoint": "It redirecting to Starting Point",
    "redirecting.blockTraining": "It redirecting to Block Your Training",
    "redirecting.keyUserInfo": "It redirecting to Key User Info",
    "redirecting.peakCapture": "It redirecting to Peak Capture",
    "redirecting.contactUs": "It redirecting to Contact Us",
  },
  jp: {
    brandDefault: "AskPLATON",
    ariaOpen: "チャットを開く",
    ariaClose: "チャットを閉じる",
    typing: "入力中…",
    openLink: "リンクを開く",
    placeholder: "ご用件を入力してください",
    hiLine1:
      "こんにちは。AskPLATONボットです。研修動画やドキュメントなどをご案内します。",
    hiLine2: "下に入力するか、クイックアクションをご利用ください。",
    errorGeneric: "問題が発生しました。もう一度お試しください。",
    errorLoad:
      "回答を読み込めませんでした。しばらくしてからもう一度お試しください。",
    noMatch:
      "一致する結果が見つかりません。別のキーワードをお試しいただくか、PLATONページをご確認ください。",
    "quick.startingPoint": "スターティングポイント",
    "quick.blockTraining": "研修のブロック",
    "quick.keyUserInfo": "キーユーザー情報",
    "quick.peakCapture": "ピークキャプチャ",
    "quick.contactUs": "お問い合わせ",
    altLogo: "プラトン",
    "redirecting.startingPoint":
      "スターティングポイントにリダイレクトしています",
    "redirecting.blockTraining": "研修のブロックにリダイレクトしています",
    "redirecting.keyUserInfo": "キーユーザー情報にリダイレクトしています",
    "redirecting.peakCapture": "ピークキャプチャにリダイレクトしています",
    "redirecting.contactUs": "お問い合わせにリダイレクトしています",
  },
};

function detectLang(explicit?: string): string {
  if (explicit && messages[explicit]) return explicit;
  const stored = (localStorage.getItem("platonLang") || "").toLowerCase();
  if (stored && messages[stored]) return stored;

  const spInfo = (window as WindowWithSP)._spPageContextInfo;
  const ui = (
    spInfo?.currentUICultureName ||
    spInfo?.currentCultureName ||
    ""
  ).toLowerCase(); // e.g., 'ja-jp'
  if (ui.startsWith("jp")) return "jp";
  return "en";
}

/* -------------------- main -------------------- */
export function initPlatonChatbot(opts?: PlatonChatOptions): PlatonChatHandle {
  let lang = detectLang(opts?.lang);
  const t = (k: MsgKey): string => messages[lang][k];

  const IDS = {
    css: "platonChatCss",
    launcher: "platonChatLauncher",
    backdrop: "platonChatBackdrop",
    window: "platonChatWindow",
    msgs: "platonChatMessages",
    input: "platonChatInput",
    send: "platonChatSend",
    title: "platonChatTitle",
    closeBtn: "platonChatClose",
    quickWrap: "platonQuickWrap",
  } as const;

  // Prevent duplicate widget
  if (document.getElementById(IDS.window)) {
    const noop = (): void => {};
    return { open: noop, close: noop, dispose: noop, setLanguage: noop };
  }

  // Discover SP web URL
  const spInfo = (window as WindowWithSP)._spPageContextInfo;
  const webAbsoluteUrl =
    opts?.webAbsoluteUrl ?? spInfo?.webAbsoluteUrl ?? window.location.origin;
  const listTitle = opts?.listTitle ?? "ChatbotQ&A";
  const brandName = opts?.brandName ?? t("brandDefault");

  // Icons & helpers
  const chatIconSVG = `<svg viewBox="0 0 24 24" width="22" height="22" aria-hidden="true"><path d="M2 4a2 2 0 0 1 2-2h16a2 2 0 0 1 2 2v11a2 2 0 0 1-2 2H8l-5 5v-5H4a2 2 0 0 1-2-2V4z" fill="currentColor"/></svg>`;
  const closeIconSVG = `<svg viewBox="0 0 24 24" width="20" height="20" aria-hidden="true"><path d="M6 6l12 12M18 6L6 18" stroke="currentColor" stroke-width="2" stroke-linecap="round"/></svg>`;

  function setLauncherIcon(opened: boolean, el: HTMLButtonElement): void {
    el.innerHTML = opened ? closeIconSVG : chatIconSVG;
    el.setAttribute("aria-label", opened ? t("ariaClose") : t("ariaOpen"));
    el.setAttribute("aria-expanded", opened ? "true" : "false");
  }

  function escapeHtml(s: string): string {
    return (s || "")
      .replace(/&/g, "&amp;")
      .replace(/</g, "&lt;")
      .replace(/>/g, "&gt;")
      .replace(/"/g, "&quot;")
      .replace(/'/g, "&#039;");
  }

  function renderLink(Link?: QAItem["Link"]): string {
    if (!Link) return "";
    if (typeof Link === "string") {
      const u = Link.trim();
      return u
        ? `<div class="pl-link"><a href="${u}" target="_blank" rel="noopener">${t(
            "openLink"
          )}</a></div>`
        : "";
    }
    const url = Link.Url || "";
    const desc = Link.Description || t("openLink");
    return url
      ? `<div class="pl-link"><a href="${url}" target="_blank" rel="noopener">${escapeHtml(
          desc
        )}</a></div>`
      : "";
  }

  function splitKeywords(s?: string): string[] {
    return (s ?? "")
      .split(/[,;]+/)
      .map((v) => v.trim().toLowerCase())
      .filter(Boolean);
  }

  // ---------- DOM ----------
  const launcher = document.createElement("button");
  launcher.id = IDS.launcher;
  launcher.type = "button";
  launcher.className = "pl-chat-launcher";
  launcher.innerHTML = chatIconSVG;
  launcher.setAttribute("aria-label", t("ariaOpen"));
  launcher.setAttribute("aria-expanded", "false");

  const backdrop = document.createElement("div");
  backdrop.id = IDS.backdrop;
  backdrop.className = "pl-chat-backdrop pl-hidden";

  const win = document.createElement("div");
  win.id = IDS.window;
  win.className = "pl-chat-window pl-hidden";
  win.innerHTML = `
    <div class="pl-chat-header">
      <div class="pl-brand">
        <span class="pl-dot"><img alt="${escapeHtml(
          t("altLogo")
        )}" src="https://corptb.sharepoint.com/sites/ProjectEngLand/_api/siteiconmanager/getsitelogo?type='1'&hash=638792923750345522" /></span>
        <div id="${IDS.title}" class="pl-title">${escapeHtml(brandName)}</div>
      </div>
      <button id="${
        IDS.closeBtn
      }" type="button" class="pl-close" aria-label="${escapeHtml(
    t("ariaClose")
  )}">×</button>
    </div>
    <div class="pl-chat-body">
      <div id="${IDS.msgs}" class="pl-messages" aria-live="polite"></div>
      <div class="pl-input">
        <input id="${IDS.input}" type="text" placeholder="${escapeHtml(
    t("placeholder")
  )}" />
        <button id="${IDS.send}" type="button" aria-label="${escapeHtml(
    t("ariaOpen")
  )}">
          <svg viewBox="0 0 24 24" width="18" height="18" aria-hidden="true"><path d="M2 21l20-9L2 3v7l14 2-14 2z" fill="currentColor"/></svg>
        </button>
      </div>
    </div>
  `;

  document.body.appendChild(launcher);
  document.body.appendChild(backdrop);
  document.body.appendChild(win);

  // ---------- CSS ----------
  if (!document.getElementById(IDS.css)) {
    const css = document.createElement("style");
    css.id = IDS.css;
    css.textContent = `
      .pl-hidden{display:none!important;}
      .pl-chat-launcher{
        position:fixed; right:22px; bottom:42px; width:56px; height:56px; border-radius:50%;
        border:none; cursor:pointer; background:#323130; color:#ffeb3b;
        display:flex; align-items:center; justify-content:center; box-shadow:0 8px 28px rgba(0,0,0,.25);
        z-index:10002; transition:transform .15s ease;
      }
      .pl-chat-launcher:hover{ transform:translateY(-1px); background:#ffeb3b; color:#323130}
      .pl-chat-backdrop{ position:fixed; inset:0; background:rgba(0,0,0,.15); z-index:10000; }
      .pl-chat-window{
        position:fixed; right:22px; bottom:110px; width:360px; max-width:92vw; background:#fff;
        border-radius:14px; box-shadow:0 10px 30px rgba(0,0,0,.25); z-index:10003; overflow:hidden;
        font-family:Arial, Helvetica, sans-serif;
      }
      .pl-chat-header{
        display:flex; align-items:center; justify-content:space-between;
        background:#000; color:#ffeb3b; padding:10px 12px;
      }
      .pl-brand{ display:flex; align-items:center; gap:8px; }
      .pl-dot{
        width:24px; height:24px; border-radius:50%; background:#ffeb3b; color:#000;
        display:flex; align-items:center; justify-content:center; font-size:12px; font-weight:700; text-transform:uppercase; overflow:hidden;
      }
      .pl-dot img{ width:100%; height:auto; }
      .pl-title{ font-weight:700; }
      .pl-close{ background:transparent; border:0; color:#ffeb3b; font-size:22px; cursor:pointer; opacity:.5;}
      .pl-close:hover{ opacity:1; }
      .pl-chat-body{ background:#fff; }
      .pl-messages{ max-height:350px; height:350px; overflow-y:auto; padding:12px; border-bottom:1px solid #f0f0f0; }
      .pl-msg{
        max-width:fit-content; margin:6px 0; padding:10px 12px; border-radius:6px; line-height:1.35; box-shadow:0 1px 1px rgba(0,0,0,.04); font-size:11px;
      }
      .pl-bot{ background:#f0f0f0; color:#424242;}
      .pl-user{ background:#ffeb3b; color:#323130; border:0; margin-left:auto; }
      .pl-typing{ opacity:.85; font-style:italic; }
      .pl-link{ font-size:12px; margin-top:6px; }
      .pl-link a{ color:#000; text-decoration:underline; }
      .pl-quick-msg{ background:transparent; border:none; box-shadow:none; padding:0; white-space:nowrap; }
      .pl-quick-wrap{ display:block; gap:8px; white-space:normal; margin:-5px 0 0 -5px; }
      .pl-quick-wrap a{ text-decoration:none; display:inline-block; margin:5px 0 0 5px; }
      .pl-quick-wrap button{
        border:0; background:#323130; color:#ffeb3b; padding:6px 12px; border-radius:999px; cursor:pointer; font-size:11px;
      }
      .pl-quick-wrap button:hover{ background:#ffeb3b; color:#323130; }
      .pl-input{ display:flex; gap:8px; padding:10px; background:#fff; }
      #${IDS.input}{ flex:1; border:1px solid #d1d5db; border-radius:999px; padding:10px 14px; outline:none; font-size:11px; color:#323130; }
      #${IDS.input}:focus{ box-shadow:0 0 0 2px #ffe852; }
      #${IDS.send}{ width:44px; height:44px; border-radius:50%; border:1px solid #323130; background:#ffeb3b; color:#000; cursor:pointer; display:flex; align-items:center; justify-content:center; }
      #${IDS.send}:hover{ background:#ffeb3b; color:#323130; }
      @media (max-width:520px){
        .pl-chat-window{ right:14px; bottom:84px; width:calc(100vw - 28px); }
        .pl-chat-launcher{ right:14px; bottom:14px; }
      }
    `;
    document.head.appendChild(css);
  }

  // ---------- Logic ----------
  const $msgs = win.querySelector(`#${IDS.msgs}`) as HTMLDivElement | null;
  const $input = win.querySelector(`#${IDS.input}`) as HTMLInputElement | null;
  const $send = win.querySelector(`#${IDS.send}`) as HTMLButtonElement | null;
  const $close = win.querySelector(
    `#${IDS.closeBtn}`
  ) as HTMLButtonElement | null;
  const $title = win.querySelector(`#${IDS.title}`) as HTMLDivElement | null;

  function scrollToBottom(): void {
    if ($msgs) $msgs.scrollTop = $msgs.scrollHeight;
  }

  function appendMsg(
    kind: "bot" | "user",
    html: string,
    extra = ""
  ): HTMLDivElement | null {
    if (!$msgs) return null;
    const row = document.createElement("div");
    row.className = `pl-msg ${
      kind === "bot" ? "pl-bot" : "pl-user"
    } ${extra}`.trim();
    row.innerHTML = html;
    $msgs.appendChild(row);
    scrollToBottom();
    return row;
  }

  function showTyping(): HTMLDivElement | null {
    return appendMsg("bot", t("typing"), "pl-typing");
  }
  function hideTyping(n: HTMLDivElement | null): void {
    if (n) n.remove();
  }

  // Q&A cache with race-safe loader
  let qaLoaded = false;
  let qaItems: QAItem[] = [];
  let loadQAPromise: Promise<void> | undefined;

  async function loadQA(): Promise<void> {
    if (qaLoaded) return;
    if (loadQAPromise) return loadQAPromise;

    const url =
      `${webAbsoluteUrl}` +
      `/_api/web/lists/getbytitle('${encodeURIComponent(listTitle)}')/items` +
      `?$select=Id,Title,Answer,Category,Keywords,Link`;

    loadQAPromise = (async (): Promise<void> => {
      const res = await fetch(url, {
        headers: { Accept: "application/json;odata=nometadata" },
        credentials: "include",
      });
      if (!res.ok) throw new Error(`Q&A fetch failed: ${res.status}`);
      const data = (await res.json()) as QaResponseNoMeta;
      qaItems = (data.value ?? []).map((r) => ({
        Id: r.Id,
        Title: r.Title || "",
        Answer: r.Answer || "",
        Category: r.Category || "",
        Keywords: r.Keywords ?? r.Keyword ?? "",
        Link: r.Link ?? r.link ?? undefined,
      }));
      qaLoaded = true;
      loadQAPromise = undefined;
    })();

    return loadQAPromise;
  }

  function findByTitle(q: string): QAItem | undefined {
    const needle = q.trim().toLowerCase();
    if (!needle) return undefined;
    return (
      qaItems.find((x) => x.Title.toLowerCase() === needle) ??
      qaItems.find((x) => x.Title.toLowerCase().includes(needle))
    );
  }

  function findByKeywords(q: string): QAItem | undefined {
    const low = q.trim().toLowerCase();
    if (!low) return undefined;
    const tokens = low.split(/\s+/).filter(Boolean);
    for (const it of qaItems) {
      const kws = splitKeywords(it.Keywords);
      if (kws.some((k) => low.includes(k))) return it;
    }
    for (const it of qaItems) {
      const kws = splitKeywords(it.Keywords);
      if (kws.some((k) => tokens.includes(k))) return it;
    }
    return undefined;
  }

  async function answerFromQA(query: string): Promise<string> {
    try {
      await loadQA();
    } catch {
      return t("errorLoad");
    }
    const item = findByTitle(query) ?? findByKeywords(query);
    if (!item) return t("noMatch");
    const body = item.Answer
      ? escapeHtml(item.Answer).replace(/\n/g, "<br/>")
      : "(No answer provided)";
    return body + renderLink(item.Link);
  }

  async function handleSend(
    force?: string,
    isQuickAction = false
  ): Promise<void> {
    const text = (force ?? ($input?.value || "")).trim();
    if (!text) return;
    appendMsg("user", escapeHtml(text));
    if ($input) $input.value = "";

    const tnode = showTyping();

    // Add 10-second delay for typed messages (not quick actions)
    if (!isQuickAction) {
      await new Promise((resolve) => setTimeout(resolve, 10000));
    }

    try {
      const html = await answerFromQA(text);
      hideTyping(tnode);
      appendMsg("bot", html);
    } catch {
      hideTyping(tnode);
      appendMsg("bot", t("errorGeneric"));
    }
  }

  // Quick actions inside messages
  function quickButtonsHTML(): string {
    return `
      <div id="${IDS.quickWrap}" class="pl-quick-wrap">
        <a href="https://corptb.sharepoint.com/sites/ProjectEngLand/SitePages/PlatonStartingPoint.aspx"><button data-q="${t(
          "quick.startingPoint"
        )}">${t("quick.startingPoint")}</button></a>
        <a href="https://corptb.sharepoint.com/sites/ProjectEngLand/SitePages/BlockYourTraining.aspx"><button data-q="${t(
          "quick.blockTraining"
        )}">${t("quick.blockTraining")}</button></a>
        <a href="https://corptb.sharepoint.com/sites/ProjectEngLand/SitePages/KeyUsersInfo.aspx"><button data-q="${t(
          "quick.keyUserInfo"
        )}">${t("quick.keyUserInfo")}</button></a>
        <a href="https://corptb.sharepoint.com/sites/ProjectEngLand/SitePages/Gallery.aspx"><button data-q="${t(
          "quick.peakCapture"
        )}">${t("quick.peakCapture")}</button></a>
        <a href="https://corptb.sharepoint.com/sites/ProjectEngLand/SitePages/ContactUs.aspx"><button data-q="${t(
          "quick.contactUs"
        )}">${t("quick.contactUs")}</button></a>
      </div>`;
  }

  function injectQuickActions(): void {
    if (!$msgs) return;
    const shell = document.createElement("div");
    shell.className = "pl-msg pl-quick-msg";
    shell.innerHTML = quickButtonsHTML();
    $msgs.appendChild(shell);

    shell.querySelectorAll("button").forEach((b) => {
      b.addEventListener("click", () => {
        const q = (b as HTMLButtonElement).getAttribute("data-q") || "";

        // Show redirection message based on the quick action
        let redirectionMessage = "";
        switch (q) {
          case t("quick.startingPoint"):
            redirectionMessage = t("redirecting.startingPoint");
            break;
          case t("quick.blockTraining"):
            redirectionMessage = t("redirecting.blockTraining");
            break;
          case t("quick.keyUserInfo"):
            redirectionMessage = t("redirecting.keyUserInfo");
            break;
          case t("quick.peakCapture"):
            redirectionMessage = t("redirecting.peakCapture");
            break;
          case t("quick.contactUs"):
            redirectionMessage = t("redirecting.contactUs");
            break;
          default:
            redirectionMessage = "It redirecting to respective page";
        }

        // Show redirection message for 10 seconds
        const redirectionNode = appendMsg(
          "bot",
          escapeHtml(redirectionMessage)
        );
        setTimeout(() => {
          if (redirectionNode) {
            redirectionNode.remove();
          }
        }, 10000);
      });
    });
    scrollToBottom();
  }

  // Open/Close
  let opened = false;
  let seeded = false;

  function open(): void {
    if (opened) return;
    win.classList.remove("pl-hidden");
    backdrop.classList.remove("pl-hidden");
    setLauncherIcon(true, launcher);
    opened = true;

    if (!seeded) {
      if ($msgs) $msgs.innerHTML = "";
      appendMsg("bot", escapeHtml(t("hiLine1")));
      appendMsg("bot", escapeHtml(t("hiLine2")));
      injectQuickActions();
      seeded = true;
    }
  }

  function close(): void {
    if (!opened) return;
    win.classList.add("pl-hidden");
    backdrop.classList.add("pl-hidden");
    setLauncherIcon(false, launcher);
    opened = false;
  }

  // Events
  launcher.addEventListener("click", () => (opened ? close() : open()));
  backdrop.addEventListener("click", () => close());
  $close?.addEventListener("click", () => close());
  document.addEventListener("keydown", (e: KeyboardEvent): void => {
    if (e.key === "Escape") close();
  });
  $send?.addEventListener("click", () => {
    handleSend().catch(() => {});
  });
  $input?.addEventListener("keydown", (e: KeyboardEventWithIME) => {
    const composing = e.isComposing === true;
    if (e.key === "Enter" && !composing) {
      e.preventDefault();
      handleSend().catch(() => {});
    }
  });

  // Live language switch (updates static UI + next messages)
  function applyStaticTexts(): void {
    launcher.setAttribute("aria-label", t("ariaOpen"));
    const closeBtn = win.querySelector(
      `#${IDS.closeBtn}`
    ) as HTMLButtonElement | null;
    closeBtn?.setAttribute("aria-label", t("ariaClose"));
    const inp = win.querySelector(`#${IDS.input}`) as HTMLInputElement | null;
    if (inp) inp.placeholder = t("placeholder");
    if ($title && !opts?.brandName) $title.textContent = t("brandDefault");

    // Update quick buttons if the container exists
    const quickContainer = win.querySelector(
      `#${IDS.quickWrap}`
    )?.parentElement;
    if (quickContainer) {
      quickContainer.innerHTML = quickButtonsHTML();
      quickContainer.querySelectorAll("button").forEach((b) =>
        b.addEventListener("click", () => {
          const q = (b as HTMLButtonElement).getAttribute("data-q") || "";

          // Show redirection message based on the quick action
          let redirectionMessage = "";
          switch (q) {
            case t("quick.startingPoint"):
              redirectionMessage = t("redirecting.startingPoint");
              break;
            case t("quick.blockTraining"):
              redirectionMessage = t("redirecting.blockTraining");
              break;
            case t("quick.keyUserInfo"):
              redirectionMessage = t("redirecting.keyUserInfo");
              break;
            case t("quick.peakCapture"):
              redirectionMessage = t("redirecting.peakCapture");
              break;
            case t("quick.contactUs"):
              redirectionMessage = t("redirecting.contactUs");
              break;
            default:
              redirectionMessage = "It redirecting to respective page";
          }

          // Show redirection message for 10 seconds
          const redirectionNode = appendMsg(
            "bot",
            escapeHtml(redirectionMessage)
          );
          setTimeout(() => {
            if (redirectionNode) {
              redirectionNode.remove();
            }
          }, 10000);
        })
      );
    }
  }

  function setLanguage(next: string): void {
    if (!messages[next]) return;
    const wasOpen = opened;

    lang = next;
    localStorage.setItem("platonLang", next);

    // update placeholders, aria labels, quick button text, etc.
    applyStaticTexts();

    // reset greeting state (clear current messages)
    if ($msgs) $msgs.innerHTML = "";
    seeded = false;

    // if chat is already open, reseed immediately
    if (wasOpen) {
      appendMsg("bot", escapeHtml(t("hiLine1")));
      appendMsg("bot", escapeHtml(t("hiLine2")));
      injectQuickActions();
      seeded = true;
    }
  }

  function dispose(): void {
    launcher.remove();
    backdrop.remove();
    win.remove();
  }

  // initial icon labels per lang
  setLauncherIcon(false, launcher);

  return { open, close, dispose, setLanguage };
}
/** ===================== /PLATON CHATBOT (Localized) ===================== */
