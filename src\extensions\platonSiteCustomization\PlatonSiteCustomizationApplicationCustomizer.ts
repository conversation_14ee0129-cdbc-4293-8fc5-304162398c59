import { override } from "@microsoft/decorators";
import { BaseApplicationCustomizer } from "@microsoft/sp-application-base";
import enUS from "./locales/en-us";
import jaJp from "./locales/ja-jp";
import { initPlatonChatbot } from "./chat/platonChatbot";

type LocaleStrings = typeof enUS;

const localeMap: Record<"en" | "jp", LocaleStrings> = {
  en: enUS,
  jp: jaJp,
};

interface PersonField {
  EMail?: string;
}
type KeyUserItem = Record<string, PersonField | undefined>;
interface ODataVerbose<T> {
  d: { results: T[] };
}

export default class PlatonSiteCustomizationApplicationCustomizer extends BaseApplicationCustomizer<{}> {
  private toggle: HTMLElement | null = null;
  private labelEN: HTMLElement | null = null;
  private labelJP: HTMLElement | null = null;
  private chatHandle: {
    open: () => void;
    close: () => void;
    dispose: () => void;
  } | null = null;
  private chat?: ReturnType<typeof initPlatonChatbot>;

  @override
  public onInit(): Promise<void> {
    const currentUrl = window.location.href;

    const oldFavicon = document.querySelector("link[rel*='icon']");
    if (oldFavicon) {
      oldFavicon.parentNode?.removeChild(oldFavicon);
    }

    // Create a new favicon link
    const link = document.createElement("link");
    link.rel = "icon";
    link.type = "image/x-icon";
    link.href =
      "https://corptb.sharepoint.com/:i:/r/sites/ProjectEngLand/SiteAssets/PlatonImages/PLATON%20ICON/png-icons/favicon.ico?csf=1&web=1&e=VOqSvd"; // Path to your favicon
    document.head.appendChild(link);

    const currentLang = localStorage.getItem("platonLang") || "en";
    const setLang = (lang: "en" | "jp"): void => {
      localStorage.setItem("platonLang", lang);
      this.toggle?.classList.remove("en", "jp");
      this.toggle?.classList.add(lang);
      this.labelEN?.classList.toggle("active", lang === "en");
      this.labelJP?.classList.toggle("active", lang === "jp");
      applyTranslations(lang);
      this.chat?.setLanguage(lang);
    };

    // Define all target page paths in an array
    const targetPagePaths = [
      "/sites/ProjectEngLand/SitePages/Home-Pages1.aspx",
      "/sites/ProjectEngLand/SitePages/PlatonStartingPoint.aspx",
      "/sites/ProjectEngLand/SitePages/Home-Pages1.aspx",
      "/sites/ProjectEngLand/SitePages/PlatonStartingPoint.aspx",
      "/sites/ProjectEngLand/SitePages/BlockYourTraining.aspx",
      "/sites/ProjectEngLand/SitePages/KeyUsersInfo.aspx",
      "/sites/ProjectEngLand/SitePages/Gallery.aspx",
      "/sites/ProjectEngLand/SitePages/PROJECT-ENGLAND(2).aspx",
      "/sites/ProjectEngLand/SitePages/ContactUs.aspx",
      "/sites/ProjectEngLand/SitePages/YetAnotherPage.aspx",
      "/sites/ProjectEngLand/SitePages/PlatonSearch.aspx",
      "/sites/ProjectEngLand/SitePages/Feedback-Form.aspx",
      "/sites/ProjectEngLand/SitePages/PeakCapture.aspx",
      "https://corptb.sharepoint.com/sites/TMTMFUSOMIGRATION/SitePages/Platon.aspx",
    ];

    // Check if the current URL includes any of the target paths
    const isTargetPage = targetPagePaths.some((path) =>
      currentUrl.includes(path)
    );

    if (!isTargetPage) {
      return Promise.resolve(); // Exit early if the current URL does not match any of the target pages
    }

    // ✅ Continue only if on the target page
    console.log("App Customizer is running on the correct page.");
    const elementsToHide = [
      "spSiteHeader",
      "spCommandBar",
      "spLeftNav",
      "sp-appBar",
      "appBar",
      "CommentsWrapper",
      "SuiteNavWrapper",
    ];

    elementsToHide.forEach((id) => {
      const element = document.getElementById(id);
      if (element) {
        element.style.display = "none";
      }
    });

    const NAV_ID = "customTopNav";
    const FOOTER_ID = "platonFooter";
    if (!document.getElementById(FOOTER_ID)) {
      const COMPANY_NAME = "MFTBC PLATON";
      const COPYRIGHT_SINCE = 2025;
      const now = new Date().getFullYear();
      const yearText =
        now > COPYRIGHT_SINCE ? `${COPYRIGHT_SINCE}-${now}` : `${now}`;

      const copyrightStyle = document.createElement("style");
      copyrightStyle.textContent = `
    .platon-footer{
      position: fixed;
      left: 0; right: 0; bottom: 0;
      background: #000;
      color: #ffeb3b;
      text-align: center;
      padding: 12px 16px;
      font-size: 14px;
      line-height: 1;
      z-index: -10001;
      box-shadow: 0 -2px 8px rgba(0,0,0,.35);
    }
    body.has-footer { padding-bottom: 46px; }
    .ub-launcher { bottom: 72px !important; }
    @media (max-width: 520px){
      .platon-footer { font-size: 13px; padding: 10px 12px; }
      body.has-footer { padding-bottom: 42px; }
      .ub-launcher { bottom: 66px !important; }
    }
  `;
      document.head.appendChild(copyrightStyle);

      const footer = document.createElement("footer");
      footer.id = FOOTER_ID;
      footer.className = "platon-footer";
      footer.textContent = `Copyright ©${yearText} ${COMPANY_NAME} All Rights Reserved`;
      document.body.appendChild(footer);
      document.body.classList.add("has-footer");
    }

    // Only bail out if BOTH header and footer already exist
    if (document.getElementById(NAV_ID) && document.getElementById(FOOTER_ID)) {
      return Promise.resolve();
    }

    // Inject Font Awesome
    if (!document.querySelector('link[href*="font-awesome"]')) {
      const fa4 = document.createElement("link");
      fa4.rel = "stylesheet";
      fa4.href =
        "https://cdnjs.cloudflare.com/ajax/libs/font-awesome/4.7.0/css/font-awesome.min.css";
      document.head.appendChild(fa4);

      const fa5 = document.createElement("link");
      fa5.rel = "stylesheet";
      fa5.href =
        "https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css";
      document.head.appendChild(fa5);
    }

    setTimeout(() => {
      const searchInput = document.getElementById(
        "spSearchInput"
      ) as HTMLInputElement;
      const searchButton = document.querySelector(
        ".search-button"
      ) as HTMLButtonElement;

      const performSearch = (): void => {
        const input = document.getElementById(
          "spSearchInput"
        ) as HTMLInputElement;
        const query = input.value.trim(); // e.g., from a text input
        if (query) {
          // ✅ Redirect to PlatonSearch.aspx with query
          window.location.href = `/sites/ProjectEngLand/SitePages/PlatonSearch.aspx?q=${encodeURIComponent(
            query
          )}`;
        }
      };

      if (searchInput) {
        searchInput.addEventListener("keypress", (event: KeyboardEvent) => {
          if (event.key === "Enter") {
            performSearch();
          }
        });
      }

      if (searchButton) {
        searchButton.addEventListener("click", () => {
          performSearch();
        });
      }

      const clearButton = document.getElementById(
        "clearSearchBtn"
      ) as HTMLButtonElement;

      if (clearButton) {
        clearButton.addEventListener("click", () => {
          if (searchInput) searchInput.value = "";
          window.history.pushState({}, "", window.location.pathname); // Reset ?q= from URL
          window.dispatchEvent(
            new CustomEvent("platonSearchChanged", { detail: "" })
          );
        });
      }
    }, 200);

    // Inject styles for top navigation and side navigation
    const style = document.createElement("style");
    style.textContent = `
      body {
        margin: 0;
        font-family: Arial, Helvetica, sans-serif;
        scrollbar-gutter: stable;
      }

      *, *::before, *::after {
        box-sizing: border-box;
      }

      .od-Header, .sp-appBar, .ms-FocusZone, .sp-pageTitle {
        display: none !important;
      }

      .topnav {
        display: flex;
        justify-content: space-between;
        align-items: center;
        background-color: white;
        position: fixed;
        top: 0;
        left: 0;
        right: 0;
        width: 100%;
        z-index: 9999;
        height: 60px;
        padding: 0 20px;
        box-shadow: 0px 5px 8px grey;
        box-sizing: border-box;
      }

      .nav-left .nav-logo {
        height: 40px;
      }

      .nav-right {
        display: flex;
        align-items: center;
        gap: 10px;
      }

      .nav-right a {
        color: black;
        text-align: center;
        padding: 0 15px;
        text-decoration: none;
        font-size: 15px;
        line-height: 60px;
      }

      .nav-right a:hover {
        background-color: lightgrey;
        color: white;
      }

      .icon {
        display: none;
        font-size: 22px;
        color: black;
        cursor: pointer;
      }

      @media screen and (min-width: 1024px) {
        .a_e_cb6f7c2e:not(.b_e_cb6f7c2e) .r_e_cb6f7c2e, .a_a_cb6f7c2e:not(.e_a_cb6f7c2e) .h_a_cb6f7c2e, .CanvasZoneSectionContainer  {
          display: flex !important;
          max-width: 100% !important; /* overrides the 1236px limit */
        }
      }

      @media screen and (max-width: 768px) {
        .topnav {
          flex-wrap: wrap;
          height: auto;
          padding: 10px;
        }

        .nav-left {
          width: 100%;
          display: flex;
          justify-content: space-between;
          align-items: center;
        }

        .nav-center {
          width: 100%;
          margin-top: 10px;
          display: flex;
          justify-content: center;
        }

        .search-input {
          width: 100%;
          max-width: none;
        }

        .search-container {
          width: 100%;
        }

        .lang-toggle {
          display: flex;
          gap: 6px;
          background: #f1f1f1;
          border-radius: 20px;
          padding: 4px;
          margin-left: 20px;
        }

        .lang-btn {
          border: none;
          padding: 6px 12px;
          border-radius: 20px;
          font-size: 13px;
          cursor: pointer;
          background-color: transparent;
          transition: background-color 0.3s ease;
        }

        .lang-btn.active {
          background-color: #0078d4;
          color: white;
        }

        .lang-btn:hover:not(.active) {
          background-color: #ddd;
        }

        .user-profile-wrapper {
          margin-top: 10px;
          width: 100%;
          justify-content: flex-start;
        }

        .icon {
          display: block;
          font-size: 22px;
          color: black;
          cursor: pointer;
        }

        .nav-right {
          flex-direction: column;
          width: 100%;
          align-items: flex-start;
          padding-top: 10px;
          background-color: white;
          display: none;
        }

        .topnav.responsive .nav-right {
          display: flex;
        }

        .topnav.responsive .nav-right a {
          display: block;
          width: 100%;
          text-align: left;
          padding: 10px 20px;
          line-height: 1.5;
        }

        .user-avatar {
          width: 28px;
          height: 28px;
        }
      }

      .nav-center {
        flex-grow: 1;
        display: flex;
        justify-content: flex-end;
        margin-right: 50px;
      }

      .search-container {
        position: relative;
        display: flex;
        align-items: center;
      }

      .search-container i {
        position: absolute;
        left: 15px;
        color: #ffeb3b;
        font-size: 14px;
      }

      .search-input {
        padding: 10px 10px 10px 30px;
        font-size: 14px;
        border-radius: 20px;
        border: 1px solid #ccc;
        width: 350px;
        transition: all 0.3s ease;
      }

      .search-input:focus {
        outline: none;
        border: 1px solid transparent;
        box-shadow: 0 0 5px rgba(0, 0, 0, 0.2);
      }

      .search-button {
        position: absolute;
        top: 50%;
        right: 0px;
        transform: translateY(-50%);
        padding: 20px;
        background-color: #000000;
        color: white;
        border: none;
        border-radius: 0px 20px 20px 0px;
        cursor: pointer;
        display: flex;
        align-items: center;
        justify-content: center;
        width: 50px;
        height: 32px;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
      }

      .user-info {
        font-size: 14px;
        color: #333;
        white-space: nowrap;
      }

      .user-profile {
        display: flex;
        align-items: center;
        margin-left: 10px;
      }

      .user-avatar {
        width: 32px;
        height: 32px;
        border-radius: 50%;
        object-fit: cover;
        border: 1px solid #ccc;
      }

      .user-profile-wrapper {
        display: flex;
        align-items: center;
        background-color: #d3d3d326;
        padding: 5px 10px;
        border-radius: 8px;
        margin-left: 10px;
        gap: 8px;
      }

      .settings {
        color: #ffeb3b;
        background: #000000;
        padding: 12px;
        border-radius: 20px;
        box-shadow: 1px 2px 8px #80808091;
      }

      /* Side navigation styles */
      .custom-side-nav { 
        position: fixed; 
        top: 60px; 
        left: 0; 
        height: calc(100vh - 60px); 
        width: 65px; 
        background: white; 
        z-index: 10000; 
        transition: left 0.3s ease, width 0.3s ease;
        box-shadow: 0px 10px 10px grey;
      }

      /* === Sidebar hover highlight === */
      .custom-side-nav li {
        position: relative;
        border-left: 3px solid transparent;
        border-radius: 8px;
        margin: 7px 6px;
        transition: background-color .2s ease, color .2s ease, border-color .2s ease;
      }

      .custom-side-nav li a {
        display: flex;
        align-items: center;
        gap: 10px;
        height: 20px;
        padding: 0;
      }

      /* Yellow background + black text/icons on hover */
      .custom-side-nav li:hover {
        background: #ffeb3b;         
        border-left-color: #000;     
      }

      .custom-side-nav li:hover a,
      .custom-side-nav li:hover i,
      .custom-side-nav li:hover span {
        color: #000 !important;       
      }

      .side-toggle-icon {
        position: absolute;
        top: 10px;
        right: -15px;
        background: white;
        border-radius: 50%;
        box-shadow: 0 2px 6px rgba(0,0,0,0.3);
        width: 30px;
        height: 30px;
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        z-index: 10000;
        transition: transform 0.3s ease;
      }

      .side-toggle-icon i {
        transition: transform 0.3s ease;
      }

      .custom-side-nav.expanded .side-toggle-icon i {
        transform: rotate(180deg);
      }

      div#sideNavToggle i {
        margin: 0px;
        color : grey;
      }

      .custom-side-nav.expanded .side-toggle-icon i {
        transform: rotate(180deg);
      }

      #workbenchPageContent,
      .CanvasZone,
      .ControlZone,
      div[class*="ControlZone"],
      div[class*="CanvasZone"],
      .section-front-page-banner {
      margin-left: 0px !important; /* match your sidebar width */
      transition: margin-left 0.3s ease;
      }

      .custom-side-nav.expanded { 
        width: 280px; 
      }

      .custom-side-nav ul { 
        list-style: none; 
        padding: 0; 
        margin: 0; 
        overflow-y: hidden;
        overflow-x: hidden;
        height: 100%;
      }

      .custom-side-nav ul:hover {
        overflow-y: scroll;
      }
        
      .custom-side-nav {
        scrollbar-gutter: stable;
      }

      .custom-side-nav li { 
        padding: 10px; 
        color: grey; 
        cursor: pointer; 
      }
        .custom-side-nav li a { 
        text-decoration:none;
        color:grey;
      }
        

      .custom-side-nav i { 
        margin: 10px 10px 10px 0; 
        font-size:20px; 
        width: 24px; 
        text-align: center; 
      }

      .custom-side-nav span { 
        opacity: 0; 
      }

      .custom-side-nav.expanded span { 
        opacity: 1; 
        transition-delay: 0.2s;
      }

      body.nav-expanded {
        margin-left: 0px; /* or add padding-left to the main content */
        transition: margin-left 0.3s ease; /* Smooth transition */
      }

      @media (max-width: 768px) {
        .custom-side-nav {
          top: 0;
          left: -200px;
          width: 60px;
        }

        .custom-side-nav.expanded {
          left: 0;
          width: 200px;
        }
      }
    `;
    // Inject styles (add this to your existing `style.textContent`)
    style.textContent =
      `
    /* Variables */
    :root {
      --side-collapsed: 65px;
      --side-expanded: 280px;
    }
    
    /* Enable side layout when sidebar is present */
    body.has-side {
      --side-width: var(--side-collapsed);
    }
    
    /* When hovered (we'll add/remove .nav-expanded via JS) */
    body.has-side.nav-expanded {
      --side-width: var(--side-expanded);
    }
    
    /* Sidebar base */
    .custom-side-nav {
      position: fixed;
      top: 60px;
      left: 0;
      height: calc(100vh - 60px);
      width: var(--side-collapsed);
      background: #fff;
      z-index: 10000;
      transition: width .25s ease;
      scrollbar-gutter: stable;
    }
    
    /* Expand width visually on hover for the sidebar itself */
    .custom-side-nav:hover {
      width: var(--side-expanded);
    }
    
    /* Labels hidden by default; reveal on hover */
    .custom-side-nav span {
      opacity: 0;
      transition: opacity .2s ease .1s;
    }
    .custom-side-nav:hover span {
      opacity: 1;
    }
    
    /* List styling (keep your existing icon/text styles) */
    .custom-side-nav ul {
      list-style: none;
      padding: 0;
      margin: 0;
      height: 100%;
      overflow: hidden;
    }
    .custom-side-nav ul:hover {
      overflow-y: auto;
    }
    
    /* Hover highlight (keep your existing rules) */
    .custom-side-nav li {
      position: relative;
      border-left: 3px solid transparent;
      border-radius: 8px;
      margin: 4px 6px;
      transition: background-color .2s ease, color .2s ease, border-color .2s ease;
    }
    
    /* Shift the main SharePoint canvas/content by side width */
    #workbenchPageContent,
    #spPageCanvasContent,
    .spCanvas-canvas,
    /* legacy */
    .CanvasZone,
    .ControlZone,
    div[class*="CanvasZone"],
    div[class*="ControlZone"],
    .sp-pageRoot,
    .spPageContent,
    .od-Layout {
      margin-left: var(--side-width) !important;
      transition: margin-left .25s ease, width .25s ease;
    }
    
    /* Keep top bar full-width */
    .topnav { left: 0; right: 0; }
    
    /* Mobile: don’t shift content; sidebar slides over */
    @media (max-width: 768px) {
      #workbenchPageContent,
      #spPageCanvasContent,
      .spCanvas-canvas,
      .CanvasZone,
      .ControlZone,
      div[class*="CanvasZone"],
      div[class*="ControlZone"],
      .sp-pageRoot,
      .spPageContent,
      .od-Layout {
        margin-left: 0 !important;
        width: 100% !important;
      }
      .custom-side-nav {
        top: 0;
        left: -200px;
        width: var(--side-collapsed);
      }
      .custom-side-nav:hover {
        left: 0;
        width: 200px;
      }
    }
    
    /* KEEP your existing topnav/search/lang toggle styles here */
    ` + style.textContent; // append your old styles
    document.head.appendChild(style);

    // Create side navigation HTML
    // Create side navigation HTML
    const sideNav = document.createElement("div");
    sideNav.className = "custom-side-nav";

    let sideNavHTML = `
  <ul>
    <li><a href="https://corptb.sharepoint.com/sites/ProjectEngLand/SitePages/Home-Pages1.aspx"><i class="fas fa-home"></i><span data-i18n-key="HomeLabel"> Home </span></a></li>
    <li><a href="https://corptb.sharepoint.com/sites/ProjectEngLand/SitePages/PlatonStartingPoint.aspx"><i class="fa fa-hourglass-start"></i><span data-i18n-key='StartingPointLabel'> Starting Point </span></a></li>
    <li data-scroll-target="short-trips"><a href="https://corptb.sharepoint.com/sites/ProjectEngLand/SitePages/Home-Pages1.aspx"><i class="fas fa-atlas"></i><span data-i18n-key='ShortTripsLabel'> PLATON Short Trips</span></a></li>
    <li data-scroll-target="depth-session"><a href="https://corptb.sharepoint.com/sites/ProjectEngLand/SitePages/Home-Pages1.aspx"><i class="fas fa-chalkboard-teacher"></i><span data-i18n-key='DepthSessionLabel'> PLATON Depth Session</span></a></li>
    <li data-scroll-target="module-series"><a href="https://corptb.sharepoint.com/sites/ProjectEngLand/SitePages/Home-Pages1.aspx"><i class="fa fa-puzzle-piece"></i><span data-i18n-key='ModuleSeriesLabel'>PLATON Module Series</span></a></li>
    <li data-scroll-target="manuals"><a href="https://corptb.sharepoint.com/sites/ProjectEngLand/SitePages/Home-Pages1.aspx"><i class="fa fa-file-alt"></i><span data-i18n-key='ManualsLabel'>PLATON Manuals</span></a></li>
    <li><a href="https://corptb.sharepoint.com/sites/ProjectEngLand/SitePages/BlockYourTraining.aspx"><i class="fa fa-check-square"></i><span data-i18n-key='BlockTrainingLabel'>  BLOCK your Training</span></a></li>
    <li data-scroll-target="system-demos"><a href="https://corptb.sharepoint.com/sites/ProjectEngLand/SitePages/Home-Pages1.aspx"><i class="fa fa-unlock"></i><span data-i18n-key='AccessPlatonLabel'> Access PLATON </span></a></li>`;

    // ✅ Add Key Users Info link only if user isKeyUser
    if (localStorage.getItem("isKeyUser") === "true") {
      sideNavHTML += `
    <li><a href="https://corptb.sharepoint.com/sites/ProjectEngLand/SitePages/KeyUsersInfo.aspx">
      <i class="fa fa-user-tie"></i><span data-i18n-key='KeyUsersLabel'> Key Users Info </span>
    </a></li>`;
    }

    sideNavHTML += `
    <li><a href="https://corptb.sharepoint.com/sites/ProjectEngLand/SitePages/PeakCapture.aspx"><i class="fa fa-chart-line"></i><span data-i18n-key='PeakCaptureLabel'> Peak Capture</span></a></li>
    <li><a href="https://corptb.sharepoint.com/sites/ProjectEngLand/SitePages/ContactUs.aspx"><i class="fa fa-envelope"></i><span data-i18n-key='ContactUsLabel'>Contact Us</span></a></li>
  </ul>
`;

    sideNav.innerHTML = sideNavHTML;
    document.body.appendChild(sideNav);
    document.body.classList.add("has-side");

    // Hover in/out to expand/shrink and shift content
    sideNav.addEventListener("mouseenter", () => {
      document.body.classList.add("nav-expanded");
    });
    sideNav.addEventListener("mouseleave", () => {
      document.body.classList.remove("nav-expanded");
    });

    setTimeout(() => {
      const menuItems = document.querySelectorAll("[data-scroll-target]");

      menuItems.forEach((item) => {
        item.addEventListener("click", (e) => {
          e.preventDefault(); // Prevent any default anchor behavior
          const targetId = item.getAttribute("data-scroll-target");
          if (targetId) {
            const section = document.getElementById(targetId);
            if (section) {
              section.scrollIntoView({ behavior: "smooth", block: "start" });
            }
          }
        });
      });
    }, 500); // Delay ensures web part sections are rendered

    // Inject top navigation HTML
    const nav = document.createElement("div");
    nav.id = NAV_ID;
    nav.innerHTML = `
      <div class="topnav" id="myTopnav">
        <div class="nav-left">
        <a href="https://corptb.sharepoint.com/sites/ProjectEngLand/SitePages/Home-Pages1.aspx">
          <img src="https://corptb.sharepoint.com/sites/ProjectEngLand/_api/siteiconmanager/getsitelogo?type='1'&hash=638792923750345522" alt="Logo" class="nav-logo" />
        </a></div>
        <div class="nav-center">
          <div class="search-container">
            <button type="submit" class="search-button">
              <i class="fas fa-search" style="font-size: 14px;"></i>
            </button>
            <input type="text" id="spSearchInput" placeholder="What do you want to learn?" class="search-input" />
          </div>
        </div>
        <div class="lang-flag-toggle">
          <span id="labelEN" class="lang-flag-label">EN</span>
          <div id="langToggle" class="lang-flag-switch">
            <div class="circle"></div>
          </div>
          <span id="labelJP" class="lang-flag-label">JP</span>
        </div>
        <div class="user-profile-wrapper">
          <div class="user-profile" id="userProfile"></div>
          <div class="user-info" id="userInfo"></div>
        </div>
      </div>
    `;
    document.body.insertBefore(nav, document.body.firstChild);

    const toggleStyle = document.createElement("style");
    toggleStyle.textContent = `
    .clear-button {
  position: absolute;
  right: 60px;
  top: 50%;
  transform: translateY(-50%);
  background: transparent;
  border: none;
  font-size: 18px;
  color: #888;
  cursor: pointer;
}

.clear-button:hover {
  color: #333;
}

      .lang-flag-toggle {
        display: flex;
        align-items: center;
        border-radius: 30px;
        padding: 5px 8px;
        width: fit-content;
        gap: 8px;
        margin-right: 15px;
      }
      .lang-flag-label {
        font-size: 14px;
        font-weight: 600;
        color: #888;
        transition: color 0.3s ease;
      }
      .lang-flag-label.active {
        color: #333;
      }
      .lang-flag-switch {
        position: relative;
        width: 46px;
        height: 26px;
        background: #ddd;
        border-radius: 26px;
        cursor: pointer;
      }
      .lang-flag-switch .circle {
        position: absolute;
        top: 2px;
        left: 2px;
        width: 22px;
        height: 22px;
        background-color: white;
        background-size: cover;
        background-position: center;
        border-radius: 50%;
        transition: transform 0.3s ease;
      }
      .lang-flag-switch.jp .circle {
        transform: translateX(20px);
        background-image: url('https://upload.wikimedia.org/wikipedia/en/9/9e/Flag_of_Japan.svg');
      }
      .lang-flag-switch.en .circle {
        transform: translateX(0);
        background-image: url('https://upload.wikimedia.org/wikipedia/en/a/ae/Flag_of_the_United_Kingdom.svg');
      }
    `;
    document.head.appendChild(toggleStyle);

    setTimeout(() => {
      this.toggle = document.getElementById("langToggle");
      this.labelEN = document.getElementById("labelEN");
      this.labelJP = document.getElementById("labelJP");

      this.toggle?.addEventListener("click", () => {
        const newLang =
          localStorage.getItem("platonLang") === "en" ? "jp" : "en";
        setLang(newLang);
      });

      // Initial language setup
      setLang(currentLang === "jp" ? "jp" : "en");
    }, 300);

    const user = this.context.pageContext.user;

    const userInfoEl = document.getElementById("userInfo");
    if (userInfoEl) {
      userInfoEl.innerHTML = `<span style="margin-left: 10px;">${user.displayName}</span>`;
    }

    const userProfileEl = document.getElementById("userProfile");
    if (userProfileEl && user.email) {
      const imgUrl = `https://outlook.office365.com/owa/service.svc/s/GetPersonaPhoto?email=${encodeURIComponent(
        user.email
      )}&size=HR64x64`;
      const fallbackImg =
        "https://cdn-icons-png.flaticon.com/512/847/847969.png"; // fallback avatar image
      userProfileEl.innerHTML = `
        <img 
          src="${imgUrl}" 
          alt="${user.displayName}" 
          title="${user.displayName}" 
          class="user-avatar"
          onerror="this.onerror=null;this.src='${fallbackImg}';"
        />
      `;
    }

    const currentUserEmail = this.context.pageContext.user.email?.toLowerCase();

    if (currentUserEmail) {
      const listName = "keyusersDetails";
      const personColumnInternalName = "Name"; // internal name for your Person/Group column

      fetch(
        `https://corptb.sharepoint.com/sites/ProjectEngLand/_api/web/lists/getbytitle('${listName}')/items?$select=${personColumnInternalName}/EMail&$expand=${personColumnInternalName}`,
        {
          headers: { Accept: "application/json;odata=verbose" },
          credentials: "include",
        }
      )
        .then((res) => res.json())
        .then((data: ODataVerbose<KeyUserItem>) => {
          const isKeyUser = data.d.results.some((item: KeyUserItem) => {
            const person = item[personColumnInternalName] as
              | PersonField
              | undefined; // still typed
            const email = person?.EMail?.toLowerCase();
            return email === currentUserEmail;
          });

          if (isKeyUser) {
            localStorage.setItem("isKeyUser", "true");
            console.log("Key user detected:", currentUserEmail);
          } else {
            localStorage.setItem("isKeyUser", "false");
          }
        })

        .catch((err) => {
          console.error("Error checking key user list:", err);
          localStorage.setItem("isKeyUser", "false");
        });
    }

    // Store the chatbot handle so you can dispose later
    // later in onInit (before the return)
    this.chat = initPlatonChatbot({
      webAbsoluteUrl: this.context.pageContext.web.absoluteUrl,
      listTitle: "ChatbotQ&A",
      brandName: "AskPLATON",
      lang: localStorage.getItem("platonLang") || "en",
    });

    return Promise.resolve();
  }

  @override
  public onDispose(): void {
    const nav = document.getElementById("customTopNav");
    if (nav) {
      nav.remove();
    }

    const sideNav = document.querySelector(".custom-side-nav");
    if (sideNav) {
      sideNav.remove();
    }

    const footer = document.getElementById("platonFooter");
    if (footer) footer.remove();
    document.body.classList.remove("has-footer");

    this.chatHandle?.dispose();
    this.chatHandle = null;
    this.chat?.dispose();
  }
}

function applyTranslations(locale: "en" | "jp"): void {
  const strings = localeMap[locale];

  const labels = document.querySelectorAll("[data-i18n-key]");
  labels.forEach((label) => {
    const key = label.getAttribute("data-i18n-key");
    if (key && strings[key as keyof LocaleStrings]) {
      label.textContent = strings[key as keyof LocaleStrings];
    }
  });

  const footerPara = document.querySelector(".footer-left p");
  if (footerPara) footerPara.textContent = strings.ContactUs;

  const footerLink = document.querySelector(".footer-links a");
  if (footerLink) footerLink.textContent = strings.SupportChannel;

  const searchInput = document.getElementById(
    "spSearchInput"
  ) as HTMLInputElement;
  if (searchInput) searchInput.placeholder = strings.SearchPlaceholder;

  // Optional: log that translations were applied
  console.log(`Applied ${locale} translations`);
}
